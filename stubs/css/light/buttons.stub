@utility btn-solid-primary {
    --btn-solid-top-shadow: var(--color-primary-500);
    --btn-solid-bottom-shadow: var(--color-primary-700);
    --btn-solid-top-shadow-hover: var(--color-primary-600);
    --btn-solid-bottom-shadow-hover: var(--color-primary-800);
    --btn-solid-color: var(--color-primary-600);
    --btn-solid-color-hover: var(--color-primary-700);
    --btn-solid-color-press: var(--color-primary-800);
}

@utility btn-solid-secondary {
    --btn-solid-top-shadow: var(--color-secondary-500);
    --btn-solid-bottom-shadow: var(--color-secondary-700);
    --btn-solid-top-shadow-hover: var(--color-secondary-600);
    --btn-solid-bottom-shadow-hover: var(--color-secondary-800);
    --btn-solid-color: var(--color-secondary-600);
    --btn-solid-color-hover: var(--color-secondary-700);
    --btn-solid-color-press: var(--color-secondary-800);
}

@utility btn-solid-success {
    --btn-solid-top-shadow: var(--color-success-500);
    --btn-solid-bottom-shadow: var(--color-success-700);
    --btn-solid-top-shadow-hover: var(--color-success-600);
    --btn-solid-bottom-shadow-hover: var(--color-success-800);
    --btn-solid-color: var(--color-success-600);
    --btn-solid-color-hover: var(--color-success-700);
    --btn-solid-color-press: var(--color-success-800);
}

@utility btn-solid-neutral {
    --btn-solid-top-shadow: var(--color-gray-800);
    --btn-solid-bottom-shadow: var(--color-gray-700);
    --btn-solid-top-shadow-hover: var(--color-gray-800);
    --btn-solid-bottom-shadow-hover: var(--color-gray-900);
    --btn-solid-color: var(--color-gray-900);
    --btn-solid-color-hover: var(--color-gray-950);
    --btn-solid-color-press: var(--color-gray-900);
}    

@utility btn-flexi-white {
    --btn-flexi-bg: var(--color-white);
    --btn-flexi-hover-bg: var(--color-gray-100);
    --btn-flexi-active-bg: var(--color-gray-50);
    --btn-flexi-shadow-a: var(--color-gray-300);
    --btn-flexi-shadow-b: var(--color-gray-200);
    --btn-flexi-shadow-c: var(--color-gray-50);
    --btn-flexi-shadow-active-a: var(--color-gray-400);
    --btn-flexi-shadow-active-b: var(--color-gray-300);
    --btn-flexi-shadow-active-c: var(--color-gray-500);
}

@utility btn-flexi-neutral {
    --btn-flexi-bg: var(--color-gray-900);
    --btn-flexi-hover-bg: var(--color-gray-800);
    --btn-flexi-active-bg: var(--color-gray-950);
    --btn-flexi-shadow-a: var(--color-gray-700);
    --btn-flexi-shadow-b: var(--color-gray-500);
    --btn-flexi-shadow-c: var(--color-gray-400);
    --btn-flexi-shadow-active-a: var(--color-gray-700);
    --btn-flexi-shadow-active-b: var(--color-gray-600);
    --btn-flexi-shadow-active-c: var(--color-gray-500);
}


@utility btn-outline-danger {
    --btn-outline-bg: var(--color-danger-50);
    --btn-outline-color: var(--color-danger-200);
    --btn-outline-text-color: var(--color-danger-800);
    --btn-outline-bg-hover: --alpha(var(--color-danger-100) / 60%);
}

@utility btn-outline-gray {
    --btn-outline-bg: var(--color-gray-50);
    --btn-outline-color: var(--color-gray-200);
    --btn-outline-text-color: var(--color-gray-800);
    --btn-outline-bg-hover: --alpha(var(--color-gray-100) / 60%);
}

@utility btn-soft-primary {
    --btn-soft-bg-color: var(--color-primary-50);
    --btn-soft-bg-color-hover: --alpha(var(--color-primary-400) / 60%);
    --btn-soft-bg-color-press: --alpha(var(--color-primary-400) / 40%);
    --btn-soft-text-color: var(--color-primary-600);
    --btn-soft-text-color-hover: var(--color-primary-700);
}

@utility btn-soft-gray {
    --btn-soft-bg-color: var(--color-gray-50);
    --btn-soft-bg-color-hover: --alpha(var(--color-gray-200) / 75%);
    --btn-soft-bg-color-press: --alpha(var(--color-gray-200) / 80%);
    --btn-soft-text-color: var(--color-gray-800);
    --btn-soft-text-color-hover: var(--color-gray-900);
}

@utility btn-ghost-primary {
    --btn-ghost-bg-color-hover: --alpha(var(--color-primary-100) / 50%);
    --btn-ghost-bg-color-press: --alpha(var(--color-primary-100) / 70%);
    --btn-ghost-text-color: var(--color-primary-600);
    --btn-ghost-text-color-hover: var(--color-primary-700);
}
@utility btn-ghost-danger {
    --btn-ghost-bg-color-hover: --alpha(var(--color-danger-100) / 50%);
    --btn-ghost-bg-color-press: --alpha(var(--color-danger-100) / 70%);
    --btn-ghost-text-color: var(--color-danger-600);
    --btn-ghost-text-color-hover: var(--color-danger-700);
}

@utility btn-ghost-gray {
    --btn-ghost-bg-color-hover: --alpha(var(--color-gray-200) / 40%);
    --btn-ghost-bg-color-press: --alpha(var(--color-gray-200) / 75%);
    --btn-ghost-text-color: var(--color-gray-800);
    --btn-ghost-text-color-hover: var(--color-gray-900);
}

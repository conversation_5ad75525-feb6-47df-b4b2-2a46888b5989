<?php

namespace Flexiwind\Installer;

use <PERSON>lexi<PERSON>\Installer\PackageInstaller;
use function Laravel\Prompts\note;

class LivewireInstaller implements InstallerInterface
{
    public function install(string $packageManager, string $dir, array $options = []): void
    {
        if (PackageInstaller::node($packageManager)->isInstalled('alpinejs')) {
            note('AlpineJS found. Uninstalling...');
            PackageInstaller::node($packageManager)->remove('alpinejs');
        }

        note('Installing livewire package');
        
        if ($options['volt']) {
            note('Adding Livewire/Volt package');
            PackageInstaller::composer()->remove('livewire/livewire');
            PackageInstaller::composer()->install('livewire/volt');
        }else{
            PackageInstaller::composer()->install('livewire/livewire');
        }
    }
}

<?php

namespace Flexiwind\Utils;

use Symfony\Component\HttpClient\HttpClient;

class HttpUtils
{
    public static function getJson(string $url, array $headers = [], array $params = []): ?array
    {
        try {
            $client = HttpClient::create();
            
            $options = [];
            if (!empty($headers)) {
                $options['headers'] = $headers;
            }
            if (!empty($params)) {
                $options['query'] = $params;
            }

            $response = $client->request('GET', $url, $options);
            
            if ($response->getStatusCode() !== 200) {
                return null;
            }

            return $response->toArray();
        } catch (\Exception $e) {
            return null;
        }
    }
}
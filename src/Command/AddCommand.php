<?php

namespace Flexiwind\Command;

use <PERSON>lex<PERSON><PERSON>\Core\RegistryBuilder;
use <PERSON>lexi<PERSON>\Utils\FileUtils;
use <PERSON>lexiwind\Utils\HttpUtils;
use <PERSON>lexiwind\Service\ProjectDetector;
use <PERSON>lex<PERSON>wind\Installer\PackageInstaller;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Yaml\Yaml;
use function Laravel\Prompts\{note, info, warning, spin, confirm};

class AddCommand extends Command
{
    private string $defaultSource;
    private array $registries;
    private string $projectRoot;
    private array $installedRegistryComponents = [];
    private array $pendingCommands = [];

    public function __construct()
    {
        parent::__construct();
        $this->projectRoot = getcwd();
        $this->loadConfiguration();
    }

    protected function configure(): void
    {
        $this
            ->setName('add')
            ->setDescription('Add UI components to your project from component registries')
            ->addArgument('components', InputArgument::IS_ARRAY | InputArgument::REQUIRED, 'Component names to add')
            ->addOption('namespace', null, InputOption::VALUE_REQUIRED, 'Namespace to use for all components')
            ->addOption('skip-deps', null, InputOption::VALUE_NONE, 'Skip dependency installation');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $components = $input->getArgument('components');
        $namespace = $input->getOption('namespace');
        $skipDeps = $input->getOption('skip-deps');

        foreach ($components as $component) {
            $this->addComponent($component, $namespace, $skipDeps);
        }

        return Command::SUCCESS;
    }

    private function loadConfiguration(): void
    {
        $configPath = $this->projectRoot . '/flexiwind.yaml';
        
        if (!file_exists($configPath)) {
            $this->defaultSource = 'http://localhost:4500/public/r/{name}.json';
            $this->registries = [];
            return;
        }

        $config = Yaml::parseFile($configPath);
        $this->defaultSource = $config['defaultSource'] ?? 'http://localhost:4500/public/r/{name}.json';
        $this->registries = $config['registries'] ?? [];
    }

    private function addComponent(string $component, ?string $namespace, bool $skipDeps = false): void
    {
        $source = $this->determineSource($component, $namespace);
        $registryJson = $this->fetchRegistry($component, $source);

        if (!$registryJson) {
            warning("⚠️ Registry not found for component: {$component}");
            return;
        }

        if (!isset($registryJson['files']) || !is_array($registryJson['files'])) {
            warning("⚠️ Invalid registry: no files for {$component}");
            return;
        }

        note("Adding component: {$component}");

        // Handle registry dependencies first
        if (!$skipDeps && isset($registryJson['registryDependencies']) && is_array($registryJson['registryDependencies'])) {
            $this->handleRegistryDependencies($registryJson['registryDependencies'], $namespace);
        }

        // Handle package dependencies
        if (!$skipDeps) {
            $this->handlePackageDependencies($registryJson);
        }
        
        spin(message: "Processing files...", callback: function () use ($registryJson) {
            foreach ($registryJson['files'] as $file) {
                $this->processFile($file);
            }
        });

        if (isset($registryJson['patch']) && is_array($registryJson['patch'])) {
            foreach ($registryJson['patch'] as $targetFile => $patches) {
                foreach ($patches as $patch) {
                    // TODO: Implement patch application
                    // FileUtils::applyPatch($targetFile, $patch);
                }
            }
        }

        // Mark this component as installed
        $this->installedRegistryComponents[] = $component;
        info("✔ {$component} added successfully");
    }

    private function handleRegistryDependencies(array $registryDependencies, ?string $namespace): void
    {
        foreach ($registryDependencies as $dependency) {
            // Skip if already installed in this session
            if (in_array($dependency, $this->installedRegistryComponents)) {
                continue;
            }

            // Check if it's already installed in the project
            if ($this->isRegistryComponentInstalled($dependency)) {
                info("Registry dependency already installed: {$dependency}");
                continue;
            }

            note("Installing registry dependency: {$dependency}");
            
            // Recursively add the dependency
            $this->addComponent($dependency, $namespace, false);
        }
    }

    private function handlePackageDependencies(array $registryJson): void
    {
        $dependencies = $registryJson['dependencies'] ?? [];
        $devDependencies = $registryJson['devDependencies'] ?? [];

        // Extract composer and node dependencies
        $composerDeps = array_merge(
            $dependencies['composer'] ?? [],
            $devDependencies['composer'] ?? []
        );
        $nodeDeps = array_merge(
            $dependencies['node'] ?? [],
            $devDependencies['node'] ?? []
        );

        if (empty($composerDeps) && empty($nodeDeps)) {
            return;
        }

        $allDeps = array_merge($composerDeps, $nodeDeps);
        note("Component requires dependencies: " . implode(', ', $allDeps));

        if (!confirm("Install dependencies now?", true)) {
            warning("⚠️ Skipping dependency installation. You may need to install them manually.");
            $this->savePendingCommands($dependencies, $devDependencies);
            return;
        }

        // Install Composer dependencies
        if (ProjectDetector::check_Composer($this->projectRoot) && !empty($composerDeps)) {
            $this->installComposerDependencies(
                $dependencies['composer'] ?? [],
                $devDependencies['composer'] ?? []
            );
        }

        // Install Node dependencies
        $packageManager = ProjectDetector::getNodePackageManager();
        if ($packageManager && file_exists($this->projectRoot . '/package.json') && !empty($nodeDeps)) {
            $this->installNodeDependencies(
                $dependencies['node'] ?? [],
                $devDependencies['node'] ?? [],
                $packageManager
            );
        }
    }

    private function installComposerDependencies(array $dependencies, array $devDependencies): void
    {
        if (empty($dependencies) && empty($devDependencies)) {
            return;
        }

        $composer = PackageInstaller::composer($this->projectRoot);
        
        if (!empty($dependencies)) {
            note("Installing Composer dependencies...");
            foreach ($dependencies as $dep) {
                $packageName = $this->extractPackageName($dep);
                if (!$composer->isInstalled($packageName)) {
                    spin(
                        message: "Installing {$dep}...",
                        callback: fn() => $composer->install($dep, false)
                    );
                } else {
                    info("Composer package already installed: {$packageName}");
                }
            }
        }

        if (!empty($devDependencies)) {
            note("Installing Composer dev dependencies...");
            foreach ($devDependencies as $dep) {
                $packageName = $this->extractPackageName($dep);
                if (!$composer->isInstalled($packageName)) {
                    spin(
                        message: "Installing {$dep} (dev)...",
                        callback: fn() => $composer->install($dep, true)
                    );
                } else {
                    info("Composer dev package already installed: {$packageName}");
                }
            }
        }
    }

    private function installNodeDependencies(array $dependencies, array $devDependencies, string $packageManager): void
    {
        if (empty($dependencies) && empty($devDependencies)) {
            return;
        }

        $node = PackageInstaller::node($packageManager, $this->projectRoot);
        
        if (!empty($dependencies)) {
            note("Installing Node.js dependencies...");
            foreach ($dependencies as $dep) {
                $packageName = $this->extractPackageName($dep);
                if (!$node->isInstalled($packageName)) {
                    spin(
                        message: "Installing {$dep}...",
                        callback: fn() => $node->install($dep, false)
                    );
                } else {
                    info("Node package already installed: {$packageName}");
                }
            }
        }

        if (!empty($devDependencies)) {
            note("Installing Node.js dev dependencies...");
            foreach ($devDependencies as $dep) {
                $packageName = $this->extractPackageName($dep);
                if (!$node->isInstalled($packageName)) {
                    spin(
                        message: "Installing {$dep} (dev)...",
                        callback: fn() => $node->install($dep, true)
                    );
                } else {
                    info("Node dev package already installed: {$packageName}");
                }
            }
        }
    }

    private function isRegistryComponentInstalled(string $component): bool
    {
        // Check if component files exist in the project
        // This is a simple check - you might want to implement a more sophisticated tracking system
        $componentDir = $this->projectRoot . '/components/' . basename($component);
        return is_dir($componentDir);
    }

    private function isComposerPackage(string $dependency): bool
    {
        // Check if it's a PHP package (contains vendor/package format)
        return preg_match('/^[a-zA-Z0-9_-]+\/[a-zA-Z0-9_-]+(@.*)?$/', $dependency);
    }

    private function isNodePackage(string $dependency): bool
    {
        // Check if it's a Node package (starts with @scope/ or is a simple name)
        return preg_match('/^(@[a-zA-Z0-9_-]+\/)?[a-zA-Z0-9_.-]+(@.*)?$/', $dependency);
    }

    private function extractPackageName(string $dependency): string
    {
        // Extract package name without version
        return explode('@', $dependency)[0];
    }

    private function determineSource(string $component, ?string $namespace): array
    {
        // If namespace is explicitly provided via --namespace option
        if ($namespace) {
            if (!isset($this->registries[$namespace])) {
                throw new \RuntimeException("Namespace {$namespace} not found in configuration.");
            }
            return $this->parseRegistryConfig($this->registries[$namespace]);
        }

        // If component starts with @namespace/
        if (str_starts_with($component, '@')) {
            $parts = explode('/', $component, 2);
            $prefix = $parts[0];
            
            if (!isset($this->registries[$prefix])) {
                throw new \RuntimeException("Namespace {$prefix} not found in configuration.");
            }
            return $this->parseRegistryConfig($this->registries[$prefix]);
        }

        // Default source for components without namespace
        return ['baseUrl' => $this->defaultSource];
    }

    private function parseRegistryConfig($config): array
    {
        if (is_string($config)) {
            return ['baseUrl' => $config];
        }

        if (is_array($config)) {
            $result = ['baseUrl' => $config['url'] ?? $config['baseUrl'] ?? ''];
            
            if (isset($config['headers'])) {
                $result['headers'] = $this->expandEnvironmentVariables($config['headers']);
            }
            
            if (isset($config['params'])) {
                $result['params'] = $config['params'];
            }
            
            return $result;
        }

        throw new \RuntimeException("Invalid registry configuration format");
    }

    private function expandEnvironmentVariables(array $headers): array
    {
        $expanded = [];
        foreach ($headers as $key => $value) {
            $expanded[$key] = preg_replace_callback('/\$\{([^}]+)\}/', function($matches) {
                return $_ENV[$matches[1]] ?? $matches[0];
            }, $value);
        }
        return $expanded;
    }

    private function fetchRegistry(string $component, array $source): ?array
    {
        // Remove namespace prefix for URL replacement
        $componentName = str_starts_with($component, '@') 
            ? explode('/', $component, 2)[1] ?? $component
            : $component;

        $url = str_replace('{name}', $componentName, $source['baseUrl']);
        $headers = $source['headers'] ?? [];
        $params = $source['params'] ?? [];

        $json = HttpUtils::getJson($url, $headers, $params);
        return is_array($json) ? $json : null;
    }

    private function processFile(array $file): void
    {
        $targetPath = $this->projectRoot . '/' . $file['target'];

        $dir = dirname($targetPath);
        if (!is_dir($dir)) {
            mkdir($dir, 0777, true);
        }

        if (file_exists($targetPath)) {
            warning("⚠️ File exists, skipping: {$file['target']}");
            return;
        }

        file_put_contents($targetPath, $file['content']);
        info("Added file: {$file['target']}");
    }

    private function savePendingCommands(array $dependencies, array $devDependencies): void
    {
        $composerDeps = $dependencies['composer'] ?? [];
        $composerDevDeps = $devDependencies['composer'] ?? [];
        $nodeDeps = $dependencies['node'] ?? [];
        $nodeDevDeps = $devDependencies['node'] ?? [];

        // Generate Composer commands
        if (!empty($composerDeps) && ProjectDetector::check_Composer($this->projectRoot)) {
            $this->pendingCommands[] = 'composer require ' . implode(' ', array_map('escapeshellarg', $composerDeps));
        }

        if (!empty($composerDevDeps) && ProjectDetector::check_Composer($this->projectRoot)) {
            $this->pendingCommands[] = 'composer require --dev ' . implode(' ', array_map('escapeshellarg', $composerDevDeps));
        }

        // Generate Node commands
        $packageManager = ProjectDetector::getNodePackageManager();
        if ($packageManager && file_exists($this->projectRoot . '/package.json')) {
            if (!empty($nodeDeps)) {
                $this->pendingCommands[] = $this->buildNodeInstallCommand($nodeDeps, false, $packageManager);
            }

            if (!empty($nodeDevDeps)) {
                $this->pendingCommands[] = $this->buildNodeInstallCommand($nodeDevDeps, true, $packageManager);
            }
        }
    }

    private function buildNodeInstallCommand(array $packages, bool $isDevDep, string $packageManager): string
    {
        $escapedPackages = array_map('escapeshellarg', $packages);
        
        return match ($packageManager) {
            'npm' => 'npm install ' . implode(' ', $escapedPackages) . ($isDevDep ? ' --save-dev' : ''),
            'yarn' => 'yarn add ' . implode(' ', $escapedPackages) . ($isDevDep ? ' --dev' : ''),
            'pnpm' => 'pnpm add ' . implode(' ', $escapedPackages) . ($isDevDep ? ' --save-dev' : ' --save'),
            'bun' => 'bun add ' . implode(' ', $escapedPackages) . ($isDevDep ? ' --development' : ''),
            default => 'npm install ' . implode(' ', $escapedPackages) . ($isDevDep ? ' --save-dev' : ''),
        };
    }

    private function showPendingCommands(): void
    {
        note("📋 Manual installation required");
        info("Run the following commands manually to install dependencies:");
        
        foreach ($this->pendingCommands as $command) {
            info("  {$command}");
        }
    }
}


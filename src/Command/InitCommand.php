<?php

namespace Flexiwind\Command;


use <PERSON>lex<PERSON><PERSON>\Service\{ProjectCreator, ProjectInitializer, ThemingInitializer, ProjectDetector};
use <PERSON>lexiwind\Libs\FlexiwindInitializer;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use function Laravel\Prompts\{note, info, spin, text};

class InitCommand extends Command
{

    public function __construct(
        private ProjectCreator $projectCreator = new ProjectCreator(),
        private ThemingInitializer $themingInitializer = new ThemingInitializer(),
        private FlexiwindInitializer $flexiwindInitializer = new FlexiwindInitializer(),
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->setName('init')
            ->setDescription('Initialize Flexiwind in your project')
            ->addOption('new-laravel', 'nl', InputOption::VALUE_NONE, 'Create a new Laravel project')
            ->addOption('new-symfony', 'ns', InputOption::VALUE_NONE, 'Create a new Symfony project')
            ->addOption('tailwind', null, InputOption::VALUE_NONE, 'Use tailwindcss')
            ->addOption('uno', null, InputOption::VALUE_NONE, 'Use UnoCSS')
            ->addOption('no-flexiwind', null, InputOption::VALUE_NONE, 'Initialize without Flexiwind UI');
    }


    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $isFlexiwind = true;
        note('⚡ Flexiwind Initializer');

        if ($input->getOption('no-flexiwind')) {
            $isFlexiwind = false;
        }

        [$projectAnswers, $initProjectFromCli] = (new ProjectInitializer())->initialize($input, $isFlexiwind);

        // For new projects, we need valid project answers
        if ($initProjectFromCli && empty($projectAnswers)) {
            return Command::FAILURE;
        }

        if ($projectAnswers['fromStarter']) {
            info('Starter projects are not yet implemented.');
            return Command::SUCCESS;
        }

        $projectPath = $initProjectFromCli
            ? $projectAnswers['projectPath'] ?? getcwd() . '/' . ($projectAnswers['name'] ?? 'my-app')
            : getcwd();



        $projectType     = ProjectDetector::detect();
        $packageManager  = ProjectDetector::getNodePackageManager();
        $themingAnswers  = $this->themingInitializer->askTheming(
            $input->getOption('tailwind') ? 'tailwindcss' : ($input->getOption('uno') ? 'unocss' : ''),
            $isFlexiwind
        );

        if ($isFlexiwind == 'flexiwind') {
            $this->flexiwindInitializer->initialize(
                $projectType,
                $packageManager,
                $projectAnswers,
                $themingAnswers,
                $projectPath,
                $input
            );
        } else {
            info('Initialization without Flexiwind is not yet implemented.');
        }

        return Command::SUCCESS;
    }
}

<?php

namespace Flexiwind\Libs;

use <PERSON>lexi<PERSON>\Core\{ConfigWriter, FileGenerator};
use <PERSON>lexi<PERSON>\Installer\PackageInstaller;
use <PERSON>lexiwind\Installer\{LivewireInstaller, AlpineInstaller, StimulusInstaller, UnoCSSInstaller, TailwindInstaller};
use Symfony\Component\Console\Input\InputInterface;
use function Laravel\Prompts\{spin, text, info};

class FlexiwindInitializer
{
    public function initialize(
        string $projectType,
        string $packageManager,
        array $projectAnswers,
        array $themingAnswers,
        string $projectPath,
        InputInterface $input
    ): bool {
        $defaultCssPath = $projectType === 'symfony' ? 'assets/styles' : 'resources/css';
        $defaultJsPath = $projectType === 'symfony' ? 'assets/js' : 'resources/js';
        $folders['css'] = text('Where do you want to place your main CSS files', $defaultCssPath, $defaultCssPath);
        $folders['js']  = text('Where do you want to place your JS files', $defaultJsPath, $defaultJsPath);
        $folders['framework'] = $projectType;
        $answers = array_merge($projectAnswers, $folders, $themingAnswers);

        $plan = [];

        // Laravel-specific
        if ($projectType === 'laravel') {
            if (!empty($answers['livewire'])) {
                $plan[] = 'livewire';
            } elseif (!empty($answers['alpine'])) {
                $plan[] = 'alpine';
            }
        }

        // Symfony-specific
        if ($projectType === 'symfony' && !empty($answers['stimulus'])) {
            $plan[] = 'stimulus';
        }

        // CSS Framework
        if (($answers['cssFramework'] ?? null) === 'unocss') {
            $plan[] = 'unocss';
        } elseif (($answers['cssFramework'] ?? null) === 'tailwindcss') {
            $plan[] = 'tailwindcss';
        }



        // Config + base files
        spin(fn() => ConfigWriter::createFlexiwindYaml($answers), "Creating config files...");
        spin(fn() => FileGenerator::generateBaseFiles($projectType, $answers), "Creating base files...");

        // Installers (strategy-based)
        $installers = [
            'livewire'   => new LivewireInstaller($answers),
            'alpine'     => new AlpineInstaller(),
            'stimulus'   => new StimulusInstaller(),
            'unocss'     => new UnoCSSInstaller(),
            'tailwindcss' => new TailwindInstaller(),
        ];

        foreach ($plan as $key) {
            spin(fn() => $installers[$key]->install($packageManager, $projectPath, $answers), "Installing {$key}...");
        }

        spin(fn() => PackageInstaller::node($packageManager)->install(''), "Installing dependencies");
        info('✔ Flexiwind initialization complete!');

        return true;
    }
}

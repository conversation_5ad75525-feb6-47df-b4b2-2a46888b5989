#!/usr/bin/env php
<?php

require __DIR__ . '/../vendor/autoload.php';

use Flexiwind\Command\BuildCommand;
use Flexiwind\Command\ValidateCommand;
use Flexiwind\Command\AddCommand;
use Symfony\Component\Console\Application;
use Flexiwind\Command\InitCommand;

$application = new Application('Flexiwind CLI', '0.1.0');

// Register commands
$application->add(new InitCommand());
$application->add(new AddCommand());
$application->add(new BuildCommand());
$application->add(new ValidateCommand());

$application->run();
